import { BookOutline, SavedProject } from '@shared/types';

/**
 * Interface for the last generated sub-chapter information
 */
export interface LastGeneratedChapter {
  mainChapterTitle: string;
  subChapterTitle: string;
  chapterIndex: number;
  subChapterIndex: number;
  index: number; // Display index (1-based)
}

/**
 * Find the most recently generated sub-chapter based on the project data
 * This looks at the generatedChapters record and finds the last chapter that was completed
 * in sequential order.
 */
export function findLastGeneratedSubChapter(
  project: SavedProject
): LastGeneratedChapter | null {
  const { outline, generatedChapters } = project;
  
  if (!outline || !generatedChapters || Object.keys(generatedChapters).length === 0) {
    return null;
  }

  let lastGenerated: LastGeneratedChapter | null = null;

  // Iterate through chapters in sequential order to find the last generated one
  for (let chapterIndex = 0; chapterIndex < outline.chapters.length; chapterIndex++) {
    const chapter = outline.chapters[chapterIndex];
    
    for (let subChapterIndex = 0; subChapterIndex < chapter.subchapters.length; subChapterIndex++) {
      const subChapter = chapter.subchapters[subChapterIndex];
      const chapterKey = `${chapter.title}-${subChapter}`;
      
      // If this chapter is generated, it could be our last generated one
      if (generatedChapters[chapterKey]) {
        lastGenerated = {
          mainChapterTitle: chapter.title,
          subChapterTitle: subChapter,
          chapterIndex,
          subChapterIndex,
          index: chapterIndex + 1 // 1-based display index
        };
      } else {
        // If we hit a non-generated chapter, we've found our boundary
        // The last generated chapter is the previous one we found
        return lastGenerated;
      }
    }
  }

  return lastGenerated;
}

/**
 * Check if a project should trigger auto-load of the last generated sub-chapter
 * Based on the project status requirements
 */
export function shouldAutoLoadLastChapter(project: SavedProject): boolean {
  const validStatuses: SavedProject['status'][] = [
    'outline_generated',
    'content_in_progress',
    'completed'
  ];

  return validStatuses.includes(project.status);
}

/**
 * Find the optimal chapter to display for completed projects
 * For completed projects, show the first chapter by default to provide a better review experience
 * For other statuses, use the last generated chapter
 */
export function findOptimalChapterForProject(
  project: SavedProject
): LastGeneratedChapter | null {
  const { outline, generatedChapters, status } = project;

  if (!outline || !generatedChapters || Object.keys(generatedChapters).length === 0) {
    return null;
  }

  // For completed projects, show the first chapter for better review experience
  if (status === 'completed') {
    return findFirstGeneratedChapter(project);
  }

  // For other statuses, use the existing logic to show last generated chapter
  return findLastGeneratedSubChapter(project);
}

/**
 * Find the first generated sub-chapter for completed projects
 * This provides a better starting point for reviewing completed books
 */
export function findFirstGeneratedChapter(
  project: SavedProject
): LastGeneratedChapter | null {
  const { outline, generatedChapters } = project;

  if (!outline || !generatedChapters || Object.keys(generatedChapters).length === 0) {
    return null;
  }

  // Find the first generated chapter in sequential order
  for (let chapterIndex = 0; chapterIndex < outline.chapters.length; chapterIndex++) {
    const chapter = outline.chapters[chapterIndex];

    for (let subChapterIndex = 0; subChapterIndex < chapter.subchapters.length; subChapterIndex++) {
      const subChapter = chapter.subchapters[subChapterIndex];
      const chapterKey = `${chapter.title}-${subChapter}`;

      // If this chapter is generated, it's our first generated one
      if (generatedChapters[chapterKey]) {
        return {
          mainChapterTitle: chapter.title,
          subChapterTitle: subChapter,
          chapterIndex,
          subChapterIndex,
          index: chapterIndex + 1 // 1-based display index
        };
      }
    }
  }

  return null;
}

/**
 * Get the next chapter that should be generated based on current progress
 * This is useful for understanding where the user left off
 */
export function getNextChapterToGenerate(
  outline: BookOutline,
  generatedChapters: Record<string, boolean>
): { chapterIndex: number; subChapterIndex: number } | null {
  if (!outline) {
    return null;
  }

  // Find the first chapter that hasn't been generated yet
  for (let chapterIndex = 0; chapterIndex < outline.chapters.length; chapterIndex++) {
    const chapter = outline.chapters[chapterIndex];
    
    for (let subChapterIndex = 0; subChapterIndex < chapter.subchapters.length; subChapterIndex++) {
      const subChapter = chapter.subchapters[subChapterIndex];
      const chapterKey = `${chapter.title}-${subChapter}`;
      
      // If this chapter hasn't been generated, it's the next one
      if (!generatedChapters[chapterKey]) {
        return { chapterIndex, subChapterIndex };
      }
    }
  }

  // All chapters have been generated
  return null;
}

/**
 * Calculate the total number of sub-chapters in an outline
 */
export function getTotalSubChaptersCount(outline: BookOutline): number {
  return outline.chapters.reduce((total, chapter) => total + chapter.subchapters.length, 0);
}

/**
 * Calculate the number of generated sub-chapters
 */
export function getGeneratedSubChaptersCount(generatedChapters: Record<string, boolean>): number {
  return Object.values(generatedChapters).filter(Boolean).length;
}

/**
 * Get progress information for a project
 */
export function getProjectProgress(project: SavedProject): {
  generated: number;
  total: number;
  percentage: number;
} {
  const total = getTotalSubChaptersCount(project.outline);
  const generated = getGeneratedSubChaptersCount(project.generatedChapters || {});
  const percentage = total > 0 ? Math.round((generated / total) * 100) : 0;
  
  return { generated, total, percentage };
}
